package me.miguel19877.dev.utils;

import me.miguel19877.dev.utils.hologram.Hologram;
import me.miguel19877.dev.utils.hologram.HologramManager;
import me.miguel19877.dev.utils.hologram.HologramBuilder;
import org.bukkit.Bukkit;
import org.bukkit.Chunk;
import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.world.ChunkLoadEvent;
import org.bukkit.plugin.Plugin;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.lang.reflect.Field;

public class ExampleHolograms implements Listener {

    private final Plugin plugin;
    private final HologramManager hologramManager;
    private final Map<String, String> hologramIds = new HashMap<>(); // Maps custom names to hologram IDs
    private final List<PendingHologram> pendingHolograms = new ArrayList<>(); // Holograms waiting for chunk load
    private int hologramCounter = 0; // Counter for generating unique IDs

    /**
     * @param plugin The plugin which uses the lib
     */
    public ExampleHolograms(Plugin plugin) {
        this.plugin = plugin;
        this.hologramManager = HologramManager.getInstance();

        // Initialize the hologram manager if not already done
        if (hologramManager != null) {
            hologramManager.initialize(plugin);
        }

        // Register this class as an event listener for chunk load events
        Bukkit.getPluginManager().registerEvents(this, plugin);

        plugin.getLogger().info("ExampleHolograms initialized with chunk loading support");
    }

    /**
     * Inner class to store pending hologram data
     */
    private static class PendingHologram {
        final String customName;
        final String hologramId;
        final Location location;
        final String text;

        PendingHologram(String customName, String hologramId, Location location, String text) {
            this.customName = customName;
            this.hologramId = hologramId;
            this.location = location;
            this.text = text;
        }
    }


    /**
     * Deactivates all holograms managed by this instance.
     */
    public void deactivateHolograms() {
        for (String hologramId : hologramIds.values()) {
            hologramManager.removeHologram(hologramId);
        }
        hologramIds.clear();
    }

    /**
     * Deactivates a specific hologram by its custom name.
     * @param customName The custom name used when creating the hologram
     */
    public void deactivateHologram(String customName) {
        String hologramId = hologramIds.get(customName);
        if (hologramId != null) {
            hologramManager.removeHologram(hologramId);
            hologramIds.remove(customName);
        }
    }

    /**
     * Deactivates a specific hologram by its Hologram object.
     * @param hologram The hologram to deactivate
     */
    public void deactivateHologram(Hologram hologram) {
        if (hologram != null) {
            hologramManager.removeHologram(hologram.getId());
            // Remove from our tracking map
            hologramIds.entrySet().removeIf(entry -> entry.getValue().equals(hologram.getId()));
        }
    }

    /**
     * Creates a hologram at the specified location with the given text.
     * If the chunk is not loaded, it will be loaded and the hologram created.
     * @param location The location where the hologram should appear
     * @param text The text to display in the hologram
     */
    public void appendHOLO(Location location, String text) {
        String customName = "holo_" + hologramCounter++;
        String hologramId = "example_" + customName;

        // Ensure the chunk is loaded before creating the hologram
        if (location.getWorld() != null && !location.getChunk().isLoaded()) {
            plugin.getLogger().info("Loading chunk for hologram '" + customName + "' at " + formatLocation(location));
            location.getChunk().load(true);
        }

        // Try to create the hologram directly using the Hologram constructor
        try {
            // Create hologram directly, bypassing HologramManager validation
            Hologram hologram = createHologramDirect(hologramId, location, text, 2.0, true);
            if (hologram != null) {
                // Add to manager registry manually
                if (addHologramToManager(hologramId, hologram)) {
                    hologramIds.put(customName, hologramId);
                    plugin.getLogger().info("Created hologram '" + customName + "' at " + formatLocation(location));
                } else {
                    plugin.getLogger().warning("Failed to register hologram '" + customName + "' with manager");
                }
            }
        } catch (Exception e) {
            // If still failing, queue it for later
            pendingHolograms.add(new PendingHologram(customName, hologramId, location.clone(), text));
            plugin.getLogger().warning("Queued hologram '" + customName + "' due to error: " + e.getMessage());
        }
    }

    /**
     * Creates a hologram at the specified location with the given text and returns the hologram object.
     * @param location The location where the hologram should appear
     * @param text The text to display in the hologram
     * @return The created hologram, or null if creation failed
     */
    public Hologram appendHOLO2(Location location, String text) {
        String customName = "holo_" + hologramCounter++;
        String hologramId = "example_" + customName;

        // Ensure the chunk is loaded before creating the hologram
        if (location.getWorld() != null && !location.getChunk().isLoaded()) {
            plugin.getLogger().info("Loading chunk for hologram '" + customName + "' at " + formatLocation(location));
            location.getChunk().load(true);
        }

        // Try to create the hologram directly
        try {
            Hologram hologram = createHologramDirect(hologramId, location, text, 2.0, true);
            if (hologram != null) {
                if (addHologramToManager(hologramId, hologram)) {
                    hologramIds.put(customName, hologramId);
                    plugin.getLogger().info("Created hologram '" + customName + "' at " + formatLocation(location));
                    return hologram;
                } else {
                    plugin.getLogger().warning("Failed to register hologram '" + customName + "' with manager");
                }
            }
        } catch (Exception e) {
            plugin.getLogger().warning("Failed to create hologram '" + customName + "': " + e.getMessage());
        }
        return null;
    }

    /**
     * Creates a hologram directly using the Hologram constructor, bypassing HologramManager validation.
     * @param id The hologram ID
     * @param location The location
     * @param text The text
     * @param heightOffset The height offset
     * @param visible Whether visible
     * @return The created hologram
     */
    private Hologram createHologramDirect(String id, Location location, String text, double heightOffset, boolean visible) {
        try {
            return new Hologram(id, location, text, heightOffset, visible);
        } catch (Exception e) {
            plugin.getLogger().severe("Failed to create hologram directly: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * Adds a hologram to the manager registry using reflection to bypass validation.
     * @param id The hologram ID
     * @param hologram The hologram instance
     * @return true if successful
     */
    private boolean addHologramToManager(String id, Hologram hologram) {
        try {
            // Use reflection to access the private holograms map
            java.lang.reflect.Field hologramsField = hologramManager.getClass().getDeclaredField("holograms");
            hologramsField.setAccessible(true);
            @SuppressWarnings("unchecked")
            java.util.Map<String, Hologram> holograms = (java.util.Map<String, Hologram>) hologramsField.get(hologramManager);
            holograms.put(id, hologram);
            return true;
        } catch (Exception e) {
            plugin.getLogger().severe("Failed to add hologram to manager registry: " + e.getMessage());
            return false;
        }
    }
    /**
     * Gets the hologram manager instance for advanced operations.
     * @return The HologramManager instance
     */
    public HologramManager getHologramManager() {
        return hologramManager;
    }

    /**
     * Gets a hologram by its custom name.
     * @param customName The custom name used when creating the hologram
     * @return The hologram, or null if not found
     */
    public Hologram getHologram(String customName) {
        String hologramId = hologramIds.get(customName);
        if (hologramId != null) {
            return hologramManager.getHologram(hologramId);
        }
        return null;
    }

    /**
     * Updates the text of a hologram by its custom name.
     * @param customName The custom name of the hologram
     * @param newText The new text to display
     * @return true if the text was updated successfully, false otherwise
     */
    public boolean updateHologramText(String customName, String newText) {
        String hologramId = hologramIds.get(customName);
        if (hologramId != null) {
            return hologramManager.updateHologramText(hologramId, newText);
        }
        return false;
    }

    /**
     * Event handler for chunk load events.
     * Creates any pending holograms when their chunks become loaded.
     */
    @EventHandler
    public void onChunkLoad(ChunkLoadEvent event) {
        if (pendingHolograms.isEmpty()) {
            return;
        }

        Chunk loadedChunk = event.getChunk();
        String worldName = loadedChunk.getWorld().getName();
        int chunkX = loadedChunk.getX();
        int chunkZ = loadedChunk.getZ();

        // Check if any pending holograms are in this chunk
        List<PendingHologram> toCreate = new ArrayList<>();
        for (PendingHologram pending : pendingHolograms) {
            if (pending.location.getWorld() != null &&
                pending.location.getWorld().getName().equals(worldName) &&
                (pending.location.getBlockX() >> 4) == chunkX &&
                (pending.location.getBlockZ() >> 4) == chunkZ) {
                toCreate.add(pending);
            }
        }

        // Create the holograms and remove them from pending list
        for (PendingHologram pending : toCreate) {
            try {
                Hologram hologram = createHologramDirect(
                    pending.hologramId,
                    pending.location,
                    pending.text,
                    2.0,
                    true
                );
                if (hologram != null && addHologramToManager(pending.hologramId, hologram)) {
                    hologramIds.put(pending.customName, pending.hologramId);
                    plugin.getLogger().info("Created queued hologram '" + pending.customName + "' at " + formatLocation(pending.location));
                } else {
                    plugin.getLogger().warning("Failed to create or register queued hologram '" + pending.customName + "'");
                }
            } catch (Exception e) {
                plugin.getLogger().warning("Failed to create queued hologram '" + pending.customName + "': " + e.getMessage());
            }
            pendingHolograms.remove(pending);
        }
    }

    /**
     * Formats a location for logging purposes.
     */
    private String formatLocation(Location location) {
        if (location.getWorld() == null) {
            return "unknown world";
        }
        return String.format("%s: %.1f, %.1f, %.1f",
            location.getWorld().getName(),
            location.getX(),
            location.getY(),
            location.getZ());
    }

    /**
     * Gets the number of pending holograms waiting for chunk loads.
     * @return The number of pending holograms
     */
    public int getPendingHologramCount() {
        return pendingHolograms.size();
    }

    /**
     * Retries creating pending holograms without forcing chunk loads.
     * This is called periodically to attempt creation when chunks become available.
     */
    public void retryPendingHolograms() {
        if (pendingHolograms.isEmpty()) {
            return;
        }

        List<PendingHologram> toProcess = new ArrayList<>(pendingHolograms);
        for (PendingHologram pending : toProcess) {
            if (pending.location.getWorld() != null && pending.location.getChunk().isLoaded()) {
                try {
                    Hologram hologram = createHologramDirect(
                        pending.hologramId,
                        pending.location,
                        pending.text,
                        2.0,
                        true
                    );
                    if (hologram != null && addHologramToManager(pending.hologramId, hologram)) {
                        hologramIds.put(pending.customName, pending.hologramId);
                        plugin.getLogger().info("Created pending hologram '" + pending.customName + "' at " + formatLocation(pending.location));
                        pendingHolograms.remove(pending);
                    }
                } catch (Exception e) {
                    plugin.getLogger().warning("Failed to create pending hologram '" + pending.customName + "': " + e.getMessage());
                }
            }
        }

        if (!pendingHolograms.isEmpty()) {
            plugin.getLogger().info(pendingHolograms.size() + " holograms still pending chunk load");
        }
    }

    /**
     * Forces creation of all pending holograms by loading their chunks.
     * Use with caution as this can cause performance issues.
     */
    public void forceCreatePendingHolograms() {
        if (pendingHolograms.isEmpty()) {
            return;
        }

        plugin.getLogger().info("Force-loading " + pendingHolograms.size() + " pending holograms...");

        List<PendingHologram> toProcess = new ArrayList<>(pendingHolograms);
        for (PendingHologram pending : toProcess) {
            if (pending.location.getWorld() != null) {
                // Force load the chunk
                pending.location.getChunk().load(true);

                try {
                    Hologram hologram = createHologramDirect(
                        pending.hologramId,
                        pending.location,
                        pending.text,
                        2.0,
                        true
                    );
                    if (hologram != null && addHologramToManager(pending.hologramId, hologram)) {
                        hologramIds.put(pending.customName, pending.hologramId);
                        plugin.getLogger().info("Force-created hologram '" + pending.customName + "' at " + formatLocation(pending.location));
                    }
                } catch (Exception e) {
                    plugin.getLogger().warning("Failed to force-create hologram '" + pending.customName + "': " + e.getMessage());
                }
                pendingHolograms.remove(pending);
            }
        }
    }

    /**
     * Test method to create a hologram at a specific location for debugging.
     * @param location The location to test
     * @param text The text to display
     * @return true if successful
     */
    public boolean testHologramCreation(Location location, String text) {
        plugin.getLogger().info("Testing hologram creation at " + formatLocation(location));

        // Check world and chunk status
        if (location.getWorld() == null) {
            plugin.getLogger().warning("World is null!");
            return false;
        }

        plugin.getLogger().info("World: " + location.getWorld().getName());
        plugin.getLogger().info("Chunk loaded: " + location.getChunk().isLoaded());

        // Force load chunk if needed
        if (!location.getChunk().isLoaded()) {
            plugin.getLogger().info("Loading chunk...");
            location.getChunk().load(true);
            plugin.getLogger().info("Chunk loaded after force: " + location.getChunk().isLoaded());
        }

        // Try direct creation
        try {
            String testId = "test_hologram_" + System.currentTimeMillis();
            Hologram hologram = createHologramDirect(testId, location, text, 2.0, true);
            if (hologram != null) {
                if (addHologramToManager(testId, hologram)) {
                    plugin.getLogger().info("Successfully created test hologram!");
                    return true;
                } else {
                    plugin.getLogger().warning("Failed to register test hologram with manager");
                }
            } else {
                plugin.getLogger().warning("Failed to create test hologram - hologram is null");
            }
        } catch (Exception e) {
            plugin.getLogger().severe("Exception creating test hologram: " + e.getMessage());
            e.printStackTrace();
        }

        return false;
    }

    /**
     * Cleanup method to be called when the plugin is disabled.
     * Removes all holograms and shuts down the hologram manager.
     */
    public void cleanup() {
        deactivateHolograms();
        pendingHolograms.clear();
        hologramManager.cleanup();
    }

    // Note: The old hologram library events (PlayerHologramShowEvent, PlayerHologramHideEvent, PlayerHologramInteractEvent)
    // are not available in the new hologram system. If you need similar functionality, you would need to implement
    // custom event handling using the new hologram system's capabilities or create custom events.
}